services:

  # ------------------------------------------------------------------
  # Cortexa Microservices Platform
  # ------------------------------------------------------------------

  voice-gateway:
    build:
      context: .
      dockerfile: services.voice-gateway/Dockerfile
    container_name: cortexa-voice-gateway
    env_file:
      - services.voice-gateway/.env.compose
    ports:
      - "8002:8002"
    expose:
      - "8002"
    networks:
      - cortexa-network
    labels:
      # Enable Traefik for this service
      - "traefik.enable=true"

      # HTTP Routes (for API endpoints, docs, health, metrics)
      - "traefik.http.routers.voice-gateway-http.rule=Host(`localhost`) && PathPrefix(`/api/v1/voice-gateway`)"
      - "traefik.http.routers.voice-gateway-http.entrypoints=web"
      - "traefik.http.routers.voice-gateway-http.service=voice-gateway-http"
      - "traefik.http.services.voice-gateway-http.loadbalancer.server.port=8002"

      # Strip the /api/v1/voice-gateway prefix before forwarding to service
      - "traefik.http.middlewares.voice-gateway-stripprefix.stripprefix.prefixes=/api/v1/voice-gateway"
      - "traefik.http.routers.voice-gateway-http.middlewares=voice-gateway-stripprefix"

      # WebSocket Routes (for voice-gateway call endpoints)
      # External: /ws/v1/voice-gateway/call/{call_id} -> Internal: /api/v1/call/ws/call/{call_id}
      - "traefik.http.routers.voice-gateway-ws.rule=Host(`localhost`) && PathPrefix(`/ws/v1/voice-gateway/call/`)"
      - "traefik.http.routers.voice-gateway-ws.entrypoints=ws"
      - "traefik.http.routers.voice-gateway-ws.service=voice-gateway-ws"
      - "traefik.http.services.voice-gateway-ws.loadbalancer.server.port=8002"

      # Transform WebSocket path: /ws/v1/voice-gateway/call/{call_id} -> /api/v1/call/ws/call/{call_id}
      - "traefik.http.middlewares.voice-gateway-ws-stripprefix.stripprefix.prefixes=/ws/v1/voice-gateway/call"
      - "traefik.http.middlewares.voice-gateway-ws-addprefix.addprefix.prefix=/api/v1/call/ws/call"
      - "traefik.http.routers.voice-gateway-ws.middlewares=voice-gateway-ws-stripprefix,voice-gateway-ws-addprefix"
    depends_on:
      kafka:
        condition: service_healthy

  # =========== sidecars ===========

  # ------------------------------------------------------------------
  # Traefik Reverse Proxy
  #
  # What: Reverse proxy for all services, provides load balancing, SSL termination, and JWT authentication
  # Why: Centralized point of entry for all services, simplifies service discovery and routing
  # ------------------------------------------------------------------

  traefik:
    image: "traefik:v2.11"
    container_name: cortexa-traefik
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.ws.address=:8000"
      - "--log.level=DEBUG"
      - "--experimental.plugins.jwt-middleware.modulename=github.com/agilezebra/jwt-middleware"
      - "--experimental.plugins.jwt-middleware.version=v1.3.2"
      - "--providers.file.directory=/etc/traefik/dynamic"
      - "--providers.file.watch=true"
    ports:
      - "80:80"     # HTTP Entrypoint
      - "8000:8000" # WebSocket Entrypoint
      - "8080:8080" # Traefik Dashboard
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "./gateway/traefik.yml:/etc/traefik/traefik.yml:ro"
      - "./gateway/dynamic-config.yml:/etc/traefik/dynamic/dynamic-config.yml:ro"
    networks:
      - cortexa-network

  # ------------------------------------------------------------------
  # Kafka and Zookeeper
  #
  # What: Message queue for inter-service communication
  # Why: Decouples services, enables asynchronous communication, and provides fault tolerance
  # ------------------------------------------------------------------

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: cortexa-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - cortexa-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: cortexa-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - cortexa-network

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: cortexa-kafka-ui
    depends_on:
      - kafka
    ports:
      - "8081:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
    networks:
      - cortexa-network

  # ------------------------------------------------------------------
  # Monitoring and Tracing
  #
  # What: Observability tools for monitoring and tracing
  # Why: Provides insights into service health, performance, and dependencies
  # ------------------------------------------------------------------

  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: cortexa-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - cortexa-network

  grafana:
    image: grafana/grafana:10.1.0
    container_name: cortexa-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      #- ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      #- ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - cortexa-network

  tempo:
    image: grafana/tempo:2.2.0
    container_name: cortexa-tempo
    command: [ "-config.file=/etc/tempo.yaml" ]
    volumes:
      - ./monitoring/tempo.yaml:/etc/tempo.yaml
      - tempo_data:/tmp/tempo
    ports:
      - "3200:3200"   # tempo
      - "4317:4317"   # otlp grpc
      - "4318:4318"   # otlp http
    networks:
      - cortexa-network


volumes:
  prometheus_data:
  grafana_data:
  tempo_data:


networks:
  cortexa-network:
    driver: bridge
