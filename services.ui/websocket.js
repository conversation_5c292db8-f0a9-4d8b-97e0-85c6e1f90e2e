// WebSocket management module
class WebSocketManager {
    constructor() {
        this.websocket = null;
        this.isRecording = false;
    }

    async connect() {
        if (!authManager.isAuthenticated()) {
            log('Please authenticate first', 'error');
            return;
        }

        const wsUrl = document.getElementById('wsUrl').value;
        const callId = document.getElementById('callId').value;
        const token = authManager.getAccessToken();

        if (!callId) {
            log('Please enter a call ID', 'error');
            return;
        }

        // Use the correct gateway WebSocket endpoint
        const fullUrl = `${wsUrl}${CONFIG.gateway.websocketPath}/${callId}`;
        
        try {
            log(`Connecting to: ${fullUrl}`, 'info');
            
            // Create WebSocket connection
            // Note: Browser WebSocket API doesn't support custom headers
            // We'll send the token in the first message after connection
            this.websocket = new WebSocket(fullUrl);
            
            this.websocket.onopen = (event) => {
                log('WebSocket connection established', 'info');
                
                // Send authentication message
                this.websocket.send(JSON.stringify({
                    type: 'auth',
                    token: token
                }));
                
                this.updateConnectionStatus('connected', 'Connected');
            };
            
            this.websocket.onmessage = (event) => {
                this.handleMessage(event);
            };
            
            this.websocket.onerror = (error) => {
                log('WebSocket error', 'error');
                console.error(error);
                this.updateConnectionStatus('error', 'Connection Error');
            };
            
            this.websocket.onclose = (event) => {
                log(`WebSocket closed: ${event.code} - ${event.reason}`, 'info');
                this.updateConnectionStatus('disconnected', 'Disconnected');
                if (audioManager) {
                    audioManager.stopRecording();
                }
                this.websocket = null;
            };
            
        } catch (error) {
            log(`Connection failed: ${error.message}`, 'error');
            this.updateConnectionStatus('error', 'Connection Failed');
        }
    }

    handleMessage(event) {
        if (event.data instanceof Blob) {
            log(`Received translated audio: ${event.data.size} bytes`, 'info');
            const audioUrl = URL.createObjectURL(event.data);
            const audioElement = document.getElementById('audioPlayback');
            audioElement.src = audioUrl;
            audioElement.play().catch(e => log(`Audio playback error: ${e.message}`, 'error'));
        } else {
            try {
                const message = JSON.parse(event.data);
                log(`Received: ${JSON.stringify(message, null, 2)}`, 'info');

                // Handle different message types
                switch (message.type) {
                    case 'connection_established':
                        log('Connection established successfully', 'info');
                        break;
                    case 'translation_result':
                        this.addTranslationResult(
                            message.original_text || 'N/A',
                            message.translated_text || 'N/A',
                            message.confidence || 0
                        );
                        break;
                    case 'config_updated':
                        log(`Configuration updated: ${JSON.stringify(message.config)}`, 'info');
                        break;
                    case 'error':
                        log(`Server error: ${message.message}`, 'error');
                        break;
                    case 'auth_success':
                        log('WebSocket authentication successful', 'info');
                        break;
                    case 'auth_failed':
                        log('WebSocket authentication failed', 'error');
                        this.disconnect();
                        break;
                    default:
                        log(`Unknown message type: ${message.type}`, 'info');
                }
            } catch (e) {
                log(`Received non-JSON message: ${event.data}`, 'info');
            }
        }
    }

    disconnect() {
        if (this.websocket) {
            this.websocket.close();
            log('Disconnecting...', 'info');
        }
        if (audioManager) {
            audioManager.stopRecording();
        }
    }

    sendMessage(message) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify(message));
            log(`Sent: ${JSON.stringify(message)}`, 'sent');
            return true;
        }
        log('WebSocket not connected', 'error');
        return false;
    }

    sendAudioData(audioData) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(audioData);
            return true;
        }
        return false;
    }

    isConnected() {
        return this.websocket && this.websocket.readyState === WebSocket.OPEN;
    }

    updateConnectionStatus(status, message) {
        const statusDiv = document.getElementById('connectionStatus');
        statusDiv.className = `status ${status}`;
        statusDiv.textContent = message;

        const isConnected = status === 'connected';
        document.getElementById('connectBtn').disabled = isConnected || !authManager.isAuthenticated();
        document.getElementById('disconnectBtn').disabled = !isConnected;
        document.getElementById('pingBtn').disabled = !isConnected;
        document.getElementById('configBtn').disabled = !isConnected;
        document.getElementById('endCallBtn').disabled = !isConnected;
        document.getElementById('recordBtn').disabled = !isConnected;
        document.getElementById('stopBtn').disabled = true;
    }

    addTranslationResult(originalText, translatedText, confidence) {
        const resultsDiv = document.getElementById('translationResults');
        const timestamp = new Date().toLocaleTimeString();

        // Clear the placeholder text if it's the first result
        const placeholder = resultsDiv.querySelector('.placeholder');
        if (placeholder) {
            placeholder.remove();
        }

        const resultDiv = document.createElement('div');
        resultDiv.className = 'translation-result';

        resultDiv.innerHTML = `
            <div class="timestamp">${timestamp} (Confidence: ${(confidence * 100).toFixed(1)}%)</div>
            <div class="original"><strong>Original:</strong> ${originalText}</div>
            <div class="translated"><strong>Translation:</strong> ${translatedText}</div>
        `;

        resultsDiv.appendChild(resultDiv);
        resultsDiv.scrollTop = resultsDiv.scrollHeight;
    }
}

// Global WebSocket manager instance
let websocketManager;

// WebSocket functions for HTML onclick handlers
async function connect() {
    await websocketManager.connect();
}

function disconnect() {
    websocketManager.disconnect();
}

function sendPing() {
    websocketManager.sendMessage({type: "ping"});
}

function sendConfigUpdate() {
    const lang = document.getElementById('targetLang').value;
    websocketManager.sendMessage({
        type: "config_update", 
        config: {target_language: lang}
    });
}

function endCall() {
    websocketManager.sendMessage({type: "end_call"});
}
